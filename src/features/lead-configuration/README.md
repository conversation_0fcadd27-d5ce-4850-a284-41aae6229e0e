# Lead Configuration Feature

A comprehensive Vue.js feature for dynamically managing lead actions, sources, and statuses in a multi-tenant CRM application.

## Features

- **Dynamic Configuration**: Manage lead actions, sources, and statuses with customizable properties
- **Real-time Updates**: Uses Firestore onSnapshot for live configuration updates
- **Color Picker**: Built-in color picker with predefined colors and custom color support
- **Icon Selection**: Choose from a comprehensive list of PrimeVue icons
- **Form Validation**: Complete validation with error handling
- **Default Protection**: Prevents deletion/disabling of all items to maintain system integrity
- **Responsive UI**: Beautiful, responsive interface using PrimeVue components

## Structure

```
src/features/lead-configuration/
├── LeadConfiguration.vue          # Main component
├── LeadConfigurationDemo.vue      # Demo/example usage
├── components/
│   ├── ConfigurationManager.vue   # Manages configuration items
│   ├── ConfigItemEditor.vue       # Edit/add individual items
│   └── ColorPickerComponent.vue   # Color picker component
├── services/
│   └── leadConfigService.ts       # Service layer for data management
├── types/
│   └── index.ts                   # TypeScript interfaces and constants
├── index.ts                       # Feature exports
└── README.md                      # This file
```

## Usage

### Basic Usage

```vue
<script setup lang="ts">
import { ref } from 'vue';
import { LeadConfiguration } from '@/features/lead-configuration';

const showConfig = ref(false);

const handleComplete = () => {
  console.log('Configuration completed');
  showConfig.value = false;
};

const handleClose = () => {
  showConfig.value = false;
};
</script>

<template>
  <div>
    <Button @click="showConfig = true">Open Lead Configuration</Button>
    
    <LeadConfigurationOnboarding
      v-if="showConfig"
      :visible="showConfig"
      @complete="handleComplete"
      @close="handleClose"
    />
  </div>
</template>
```

### Service Usage

```typescript
import { LeadConfigService } from '@/features/lead-configuration';

// Get current tenant configuration
const config = await LeadConfigService.getTenantConfiguration();

// Update configuration
const success = await LeadConfigService.updateTenantConfiguration(
  'lead_actions',
  updatedConfig
);

// Subscribe to real-time updates
const unsubscribe = LeadConfigService.subscribeToTenantChanges((config) => {
  console.log('Configuration updated:', config);
});

// Clean up subscription
LeadConfigService.unsubscribeFromTenantChanges();
```

## Configuration Structure

Each configuration item has the following structure:

```typescript
interface LeadConfigItem {
  bg_color: string;      // Hex color for background
  icon: string;          // PrimeVue icon class
  label: string;         // Display label
  text_color: string;    // Hex color for text
  status: 'enabled' | 'disabled';
}
```

### Default Configurations

#### Lead Actions
- **booked**: Appointment booked
- **inquired**: Customer inquiry received
- **quoted**: Quote provided
- **converted**: Lead converted to customer

#### Lead Sources
- **ai_call**: AI-generated call
- **whatsapp**: WhatsApp message
- **facebook**: Facebook lead
- **email**: Email inquiry
- **call**: Phone call

#### Lead Statuses
- **cold**: Cold lead
- **warm**: Warm lead
- **hot**: Hot lead

## Integration with Tenant System

The feature integrates with the existing tenant system by:

1. **Authentication**: Uses `useAuthStore().getUserData()` to get the current user's tenant ID
2. **Tenant Store**: Uses `useTenantStore().getTenant()` to fetch tenant data
3. **Real-time Updates**: Subscribes to tenant document changes using Firestore onSnapshot
4. **Data Persistence**: Stores configuration in the tenant document under `lead_actions`, `lead_sources`, and `lead_statuses` fields

## Validation Rules

- **Label**: Required, max 50 characters
- **Icon**: Required, must be a valid PrimeVue icon class
- **Colors**: Required, must be valid hex colors (#RRGGBB or #RGB)
- **Key**: Required for new items, lowercase letters/numbers/underscores only
- **Status**: At least one item must remain enabled per configuration type

## Customization

### Adding New Icons

Add new icons to the `AVAILABLE_ICONS` array in `types/index.ts`:

```typescript
export const AVAILABLE_ICONS = [
  // ... existing icons
  { label: 'New Icon', value: 'pi pi-new-icon' }
];
```

### Adding New Predefined Colors

Add new colors to the `PREDEFINED_COLORS` array in `types/index.ts`:

```typescript
export const PREDEFINED_COLORS = [
  // ... existing colors
  '#FF6B6B' // New color
];
```

### Extending Configuration Types

To add new configuration types, update the `ConfigurationType` and `CONFIGURATION_SECTIONS` in `types/index.ts`.

## Events

### LeadConfiguration Component

- `@complete`: Emitted when user completes configuration
- `@close`: Emitted when user closes the configuration

### ConfigurationManager Component

- `@update`: Emitted when configuration is updated
- `@changes`: Emitted when there are unsaved changes

## Dependencies

- Vue 3
- PrimeVue
- Pinia (for state management)
- Firebase/Firestore
- TypeScript

## Notes

- The feature requires a logged-in user with a valid tenant ID
- Configuration changes are automatically saved to Firestore
- Real-time updates ensure all users see the latest configuration
- Default items cannot be deleted but can be modified
- At least one item must remain enabled in each configuration type
