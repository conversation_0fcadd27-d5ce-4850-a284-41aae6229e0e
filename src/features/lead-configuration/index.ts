// Main feature component
export { default as OnboardingLeadConfiguration } from './OnboardingLeadConfiguration.vue';

// Components
export { default as ConfigurationManager } from './components/ConfigurationManager.vue';
export { default as ConfigItemEditor } from './components/ConfigItemEditor.vue';
export { default as ColorPickerComponent } from './components/ColorPickerComponent.vue';

// Services
export { LeadConfigService } from './services/leadConfigService';

// Types
export type { LeadConfigItem, LeadActionsConfig, LeadSourcesConfig, LeadStatusesConfig, LeadConfiguration, LeadConfigErrors, EditableConfigItem, ConfigurationType, ConfigurationSection } from './types';

// Constants and defaults
export { DEFAULT_LEAD_ACTIONS, DEFAULT_LEAD_SOURCES, DEFAULT_LEAD_STATUSES, AVAILABLE_ICONS, PREDEFINED_COLORS, CONFIGURATION_SECTIONS } from './types';
